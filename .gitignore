# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile '~/.gitignore_global'

# Ignore bundler config.
/.bundle

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep
.env
/node_modules
/yarn-error.log

.byebug_history

#Ignore yml files in config
Gemfile.lock
config/database.yml
config/secrets.yml
config/application.yml
config/couchdb.yml
config/emr_connection.yml
config/results_channel_socket.yml
config/master_nlims.yml
config/local_nlims.yml
config/settings.yml
bin/system_config.rb
users_credentials.txt

# Ignore db schema
db/schema.rb
public/tracker.json

/config/master.key
