# Be sure to restart your server when you modify this file.

# Your secret key is used for verifying the integrity of signed cookies.
# If you change this key, all old signed cookies will become invalid!

# Make sure the secret is at least 30 characters and all random,
# no regular words or you'll be exposed to dictionary attacks.
# You can use `rails secret` to generate a secure secret key.

# Make sure the secrets in this file are kept private
# if you're sharing your code publicly.

# Shared secrets are available across all environments.

# shared:
#   api_key: a1B2c3D4e5F6

# Environmental secrets are only available for that specific environment.

development:
  secret_key_base: 9a29ebe56767d73e78d6160f61693124f717d02c17c2b64f47ca9685d917ee25297f7522415d3f227907fcc1d72500a666d7f4139b919b5d5b3b2ab0e20fb96b

test:
  secret_key_base: ad54bab3a17d4ebf91878324280b8020d3767f60794374d43820d6b86799d56d6e3c7d95659449b7145144f4234c35f8807852a01998448ddf856160729258b8

# Do not keep production secrets in the unencrypted secrets file.
# Instead, either read values from the environment.
# Or, use `bin/rails secrets:setup` to configure encrypted secrets
# and move the `production:` environment over there.

production:
  secret_key_base: <%= ENV["SECRET_KEY_BASE"] %>
