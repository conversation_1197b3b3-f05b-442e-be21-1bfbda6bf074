<h1 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">Counts by Sending Facility</h1>

<div style="display: flex; justify-content: center; margin-bottom: 30px;">
  <div style="background: white; padding: 25px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #dee2e6; min-width: 400px;">
    <form action="<%= count_by_sending_facility_path %>" method="get" style="display: flex; gap: 20px; align-items: end; flex-wrap: wrap;">
      <div style="display: flex; flex-direction: column; gap: 8px; flex: 1; min-width: 150px;">
        <label for="from_date" style="font-weight: 500; color: #495057; font-size: 14px;">From Date:</label>
        <input type="date" name="from_date" id="from_date" value="<%= params[:from_date] || Date.today %>"
               style="padding: 10px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
      </div>

      <div style="display: flex; flex-direction: column; gap: 8px; flex: 1; min-width: 150px;">
        <label for="to_date" style="font-weight: 500; color: #495057; font-size: 14px;">To Date:</label>
        <input type="date" name="to_date" id="to_date" value="<%= params[:to_date] || Date.today %>"
               style="padding: 10px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px;">
      </div>

      <div style="flex: 0 0 auto;">
        <button type="submit" style="background: linear-gradient(135deg, #0066cc 0%, #004499 100%); color: white; border: none; padding: 12px 20px; border-radius: 4px; font-size: 14px; font-weight: 500; cursor: pointer; transition: opacity 0.2s;"
                onmouseover="this.style.opacity='0.9'" onmouseout="this.style.opacity='1'">
          Search Counts
        </button>
      </div>
    </form>
  </div>
</div>

<% if @count_data[:data].any? %>
  <div style="display: flex; justify-content: center;">
    <table style="border: 1px solid #dee2e6; width: 70%; border-collapse: collapse; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-radius: 8px; overflow: hidden;">
      <thead>
        <tr style="background: linear-gradient(135deg, #0066cc 0%, #004499 100%); color: white;">
          <th style="padding: 15px; border: 1px solid #dee2e6; font-size: 14px; font-weight: 600; text-align: left;">Sending Facility</th>
          <th style="padding: 15px; border: 1px solid #dee2e6; font-size: 14px; font-weight: 600; text-align: center;">Total Count</th>
        </tr>
      </thead>
      <tbody>
        <% @count_data[:data].each_with_index do |(facility, count), index| %>
          <tr style="background-color: <%= index.even? ? '#f8f9fa' : 'white' %>; transition: background-color 0.2s;"
              onmouseover="this.style.backgroundColor='#e3f2fd'"
              onmouseout="this.style.backgroundColor='<%= index.even? ? '#f8f9fa' : 'white' %>'">
            <td style="padding: 12px 15px; border: 1px solid #dee2e6; font-size: 13px; text-align: left; font-weight: 500; color: #495057;"><%= facility %></td>
            <td style="padding: 12px 15px; border: 1px solid #dee2e6; font-size: 13px; text-align: center; font-weight: 600; color: #007bff;"><%= count %></td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
<% else %>
  <div style="text-align: center; margin-top: 30px;">
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 20px; border-radius: 8px; display: inline-block;">
      <strong>No data available</strong> for the selected date range.
    </div>
  </div>
<% end %>
