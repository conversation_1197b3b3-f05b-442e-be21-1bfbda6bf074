-- MySQL dump 10.13  Distrib 8.0.44, for Linux (x86_64)
--
-- Host: 127.0.0.1    Database: catalog
-- ------------------------------------------------------
-- Server version	8.0.44-0ubuntu0.24.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `versions`
--

DROP TABLE IF EXISTS `versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `versions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `whodunnit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` datetime(6) DEFAULT NULL,
  `item_id` bigint NOT NULL,
  `item_type` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `event` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `object` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `object_changes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`id`),
  KEY `index_versions_on_item_type_and_item_id` (`item_type`,`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=155 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `versions`
--

LOCK TABLES `versions` WRITE;
/*!40000 ALTER TABLE `versions` DISABLE KEYS */;
INSERT INTO `versions` VALUES (1,'1','2025-10-29 15:01:51.000000',61,'Measure','update','---\nshort_name: WBC\nnlims_code: NLIMS_TI_0605_MWI\nname: White Blood Cell\npreferred_name: White Blood Cells\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 61\nunit: cells/cu.mm\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: White Blood Cell\niblis_mapping_name: WBC\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:51.000000000 +02:00\npreferred_name:\n- White Blood Cells\n- WBC\n'),(2,'1','2025-10-29 15:01:51.000000',62,'Measure','update','---\nshort_name: RBC\nnlims_code: NLIMS_TI_0604_MWI\nname: Red Blood Cell\npreferred_name: Red Blood Cells\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 62\nunit: cells/cu.mm\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Red Blood Cell\niblis_mapping_name: RBC\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:51.000000000 +02:00\npreferred_name:\n- Red Blood Cells\n- RBC\n'),(3,'1','2025-10-29 15:01:51.000000',796,'Measure','update','---\nshort_name: Microscopy findings\nnlims_code: NLIMS_TI_0821_MWI\nname: Microscopy Findings\npreferred_name: Microscopy Findings\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 4\nid: 796\nunit: \'\'\ncreated_at: 2025-08-11 15:40:14.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Microscopy findings\niblis_mapping_name:\n','---\nname:\n- Microscopy Findings\n- Microscopy findings\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:51.000000000 +02:00\npreferred_name:\n- Microscopy Findings\n- Microscopy findings\n'),(4,'1','2025-10-29 15:01:51.000000',80,'Measure','update','---\nshort_name: \'Crystals \'\nnlims_code: NLIMS_TI_0080_MWI\nname: Crystals (Calcium Oxalate)\npreferred_name: Crystals\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 2\nid: 80\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:04.000000000 +02:00\nscientific_name: Crystals (Calcium Oxalate)\niblis_mapping_name: Crystals\n','---\nupdated_at:\n- 2025-09-02 05:07:04.000000000 +02:00\n- 2025-10-29 15:01:51.000000000 +02:00\npreferred_name:\n- Crystals\n- \'Crystals \'\n'),(5,'1','2025-10-29 15:01:51.000000',956,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 956\ntest_type_id:\n-\n- 12\nmeasure_id:\n-\n- 61\ncreated_at:\n-\n- 2025-10-29 15:01:51.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:51.000000000 +02:00\n'),(6,'1','2025-10-29 15:01:51.000000',957,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 957\ntest_type_id:\n-\n- 12\nmeasure_id:\n-\n- 62\ncreated_at:\n-\n- 2025-10-29 15:01:51.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:51.000000000 +02:00\n'),(7,'1','2025-10-29 15:01:52.000000',87,'Measure','update','---\nshort_name: TP\nnlims_code: NLIMS_TT_0079_MWI\nname: Total Protein\npreferred_name: Total Protein\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 87\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:04.000000000 +02:00\nscientific_name: Protein\niblis_mapping_name: Protein\n','---\nupdated_at:\n- 2025-09-02 05:07:04.000000000 +02:00\n- 2025-10-29 15:01:52.000000000 +02:00\npreferred_name:\n- Total Protein\n- Protein\n'),(8,'1','2025-10-29 15:01:52.000000',91,'Measure','update','---\nshort_name: Specific gravity\nnlims_code: NLIMS_TI_0827_MWI\nname: Specific Gravity\npreferred_name: Specific Gravity\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 91\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Specific gravity\niblis_mapping_name: Specific gravity\n','---\nname:\n- Specific Gravity\n- Specific gravity\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:52.000000000 +02:00\npreferred_name:\n- Specific Gravity\n- Specific gravity\n'),(9,'1','2025-10-29 15:01:52.000000',92,'Measure','update','---\nshort_name: Leucocytes\nnlims_code: NLIMS_TI_0092_MWI\nname: Leucocytes\npreferred_name: Leucocytes\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 92\nunit: WBC/ul\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-05-13 11:08:55.000000000 +02:00\nscientific_name: Leucocytes\niblis_mapping_name: Leucocytes\n','---\nname:\n- Leucocytes\n- Leukocytes\nupdated_at:\n- 2025-05-13 11:08:55.000000000 +02:00\n- 2025-10-29 15:01:52.000000000 +02:00\npreferred_name:\n- Leucocytes\n- Leukocytes\nshort_name:\n- Leucocytes\n- Leukocytes\n'),(10,'1','2025-10-29 15:01:53.000000',958,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 958\ntest_type_id:\n-\n- 18\nmeasure_id:\n-\n- 61\ncreated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\n'),(11,'1','2025-10-29 15:01:53.000000',113,'Measure','update','---\nshort_name: VDRL\nnlims_code: NLIMS_TT_0127_MWI\nname: Venereal Disease Research Laboratory\npreferred_name: Vdrl\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 113\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Venereal Disease Research Laboratory\niblis_mapping_name: VDRL\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:53.000000000 +02:00\npreferred_name:\n- Vdrl\n- VDRL\n'),(12,'1','2025-10-29 15:01:53.000000',959,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 959\ntest_type_id:\n-\n- 19\nmeasure_id:\n-\n- 112\ncreated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\n'),(13,'1','2025-10-29 15:01:53.000000',960,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 960\ntest_type_id:\n-\n- 19\nmeasure_id:\n-\n- 113\ncreated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\n'),(14,'1','2025-10-29 15:01:53.000000',961,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 961\ntest_type_id:\n-\n- 19\nmeasure_id:\n-\n- 114\ncreated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:53.000000000 +02:00\n'),(15,'1','2025-10-29 15:01:54.000000',87,'Measure','update','---\nshort_name: TP\nnlims_code: NLIMS_TT_0079_MWI\nname: Total Protein\npreferred_name: Protein\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 87\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:52.000000000 +02:00\nscientific_name: Protein\niblis_mapping_name: Protein\n','---\nupdated_at:\n- 2025-10-29 15:01:52.000000000 +02:00\n- 2025-10-29 15:01:54.000000000 +02:00\npreferred_name:\n- Protein\n- Total Protein\nshort_name:\n- TP\n- PRO\n'),(16,'1','2025-10-29 15:01:54.000000',962,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 962\ntest_type_id:\n-\n- 32\nmeasure_id:\n-\n- 87\ncreated_at:\n-\n- 2025-10-29 15:01:54.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:54.000000000 +02:00\n'),(17,'1','2025-10-29 15:01:54.000000',148,'Measure','update','---\nshort_name: HGB\nnlims_code: NLIMS_TT_0105_MWI\nname: Haemoglobin\npreferred_name: Haemoglobin\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 148\nunit: g/dL\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:07:04.000000000 +02:00\nscientific_name: \'Haemoglobin \'\niblis_mapping_name: HGB\n','---\nname:\n- Haemoglobin\n- \'Haemoglobin \'\nupdated_at:\n- 2025-09-02 05:07:04.000000000 +02:00\n- 2025-10-29 15:01:54.000000000 +02:00\npreferred_name:\n- Haemoglobin\n- HGB\n'),(18,'1','2025-10-29 15:01:54.000000',149,'Measure','update','---\nshort_name: HCT\nnlims_code: NLIMS_TI_0149_MWI\nname: Hematocrit\npreferred_name: HCT\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 149\nunit: \"%\"\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Hematocrit \'\niblis_mapping_name: HCT\n','---\nname:\n- Hematocrit\n- \'Hematocrit \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:54.000000000 +02:00\n'),(19,'1','2025-10-29 15:01:54.000000',567,'Measure','update','---\nshort_name: RET#\nnlims_code: NLIMS_TI_0635_MWI\nname: Absolute Reticulocyte Count\npreferred_name: Reticulocyte Count\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 567\nunit: 10^6/uL\ncreated_at: 2025-04-19 21:55:24.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Absolute Reticulocyte Count\niblis_mapping_name: RET#\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:54.000000000 +02:00\npreferred_name:\n- Reticulocyte Count\n- RET#\n'),(20,'1','2025-10-29 15:01:54.000000',963,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 963\ntest_type_id:\n-\n- 35\nmeasure_id:\n-\n- 61\ncreated_at:\n-\n- 2025-10-29 15:01:54.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:54.000000000 +02:00\n'),(21,'1','2025-10-29 15:01:54.000000',964,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 964\ntest_type_id:\n-\n- 35\nmeasure_id:\n-\n- 62\ncreated_at:\n-\n- 2025-10-29 15:01:54.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:54.000000000 +02:00\n'),(22,'1','2025-10-29 15:01:54.000000',185,'Measure','update','---\nshort_name: K\nnlims_code: NLIMS_TI_0185_MWI\nname: Potassium\npreferred_name: K\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 185\nunit: mmol/L\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Potassium \'\niblis_mapping_name: K\n','---\nname:\n- Potassium\n- \'Potassium \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:54.000000000 +02:00\n'),(23,'1','2025-10-29 15:01:54.000000',186,'Measure','update','---\nshort_name: Na\nnlims_code: NLIMS_TI_0186_MWI\nname: Sodium\npreferred_name: Na\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 186\nunit: mmol/L\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Sodium \'\niblis_mapping_name: Na\n','---\nname:\n- Sodium\n- \'Sodium \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:54.000000000 +02:00\n'),(24,'1','2025-10-29 15:01:55.000000',187,'Measure','update','---\nshort_name: Cl\nnlims_code: NLIMS_TI_0187_MWI\nname: Chloride\npreferred_name: Cl\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 187\nunit: mmol/L\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Chloride \'\niblis_mapping_name: Cl\n','---\nname:\n- Chloride\n- \'Chloride \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(25,'1','2025-10-29 15:01:55.000000',243,'Measure','update','---\nshort_name: Mg\nnlims_code: NLIMS_TT_0050_MWI\nname: Magnesium\npreferred_name: Mg\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 243\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Magnesium\niblis_mapping_name: Mg\n','---\nname:\n- Magnesium\n- \'Magnesium \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(26,'1','2025-10-29 15:01:55.000000',241,'Measure','update','---\nshort_name: Ca\nnlims_code: NLIMS_TT_0048_MWI\nname: Calcium\npreferred_name: Ca\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 241\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Calcium\niblis_mapping_name: Ca\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(27,'1','2025-10-29 15:01:55.000000',242,'Measure','update','---\nshort_name: P\nnlims_code: NLIMS_TT_0049_MWI\nname: Phosphorus\npreferred_name: Phos\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 242\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Phosphorus\niblis_mapping_name: P\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\nshort_name:\n- P\n- Phos\n'),(28,'1','2025-10-29 15:01:55.000000',965,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 965\ntest_type_id:\n-\n- 36\nmeasure_id:\n-\n- 243\ncreated_at:\n-\n- 2025-10-29 15:01:55.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(29,'1','2025-10-29 15:01:55.000000',966,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 966\ntest_type_id:\n-\n- 36\nmeasure_id:\n-\n- 241\ncreated_at:\n-\n- 2025-10-29 15:01:55.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(30,'1','2025-10-29 15:01:55.000000',967,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 967\ntest_type_id:\n-\n- 36\nmeasure_id:\n-\n- 242\ncreated_at:\n-\n- 2025-10-29 15:01:55.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(31,'1','2025-10-29 15:01:55.000000',205,'Measure','update','---\nshort_name: PT\nnlims_code: NLIMS_TT_0039_MWI\nname: Prothrombin Time\npreferred_name: PT\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 205\nunit: sec\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Prothrombin time \'\niblis_mapping_name: PT\n','---\nname:\n- Prothrombin Time\n- \'Prothrombin Time \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(32,'1','2025-10-29 15:01:55.000000',41,'TestType','update','---\nshort_name: INR\nnlims_code: NLIMS_TT_0041_MWI\nname: International Normalized Ratio\npreferred_name: INR\nmoh_code:\nloinc_code:\ndescription: \"<p>To monitor performance of anticoagulant medications&nbsp;(INR)</p>\"\nid: 41\ntest_category_id: 2\ntargetTAT: 2 Hours\nprevalence_threshold: \'\'\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-09-02 05:00:47.000000000 +02:00\nscientific_name: \'International Normalized Ratio \'\ncan_be_done_on_sex: Both\nassay_format: Hand-held or automated coagulation analyser\nhr_cadre_required: Lab technician\niblis_mapping_name: INR\n','---\nname:\n- International Normalized Ratio\n- \'International Normalized Ratio \'\nupdated_at:\n- 2025-09-02 05:00:47.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(33,'1','2025-10-29 15:01:55.000000',207,'Measure','update','---\nshort_name: INR\nnlims_code: NLIMS_TT_0041_MWI\nname: International Normalized Ratio\npreferred_name: INR\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 207\nunit: \'\'\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'International Normalized Ratio \'\niblis_mapping_name: INR\n','---\nname:\n- International Normalized Ratio\n- \'International Normalized Ratio \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(34,'1','2025-10-29 15:01:55.000000',47,'TestType','update','---\nshort_name: DAT\nnlims_code: NLIMS_TT_0047_MWI\nname: Direct Antiglobulin Test\npreferred_name: Direct Coombs Test\nmoh_code:\nloinc_code:\ndescription: \"<p>To aid in the diagnosis of the cause of immune haemolytic anaemias,\n  To investigate a blood transfusion reaction, To diagnose haemolytic disease of the\n  newborn (HDNB</p>\"\nid: 47\ntest_category_id: 3\ntargetTAT: 4 Hours\nprevalence_threshold: \'\'\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-09-02 05:00:47.000000000 +02:00\nscientific_name: Direct Coombs Test\ncan_be_done_on_sex: Both\nassay_format: \'Haemagglutination \'\nhr_cadre_required: Lab technician\niblis_mapping_name: Direct Coombs Test\n','---\nname:\n- Direct Antiglobulin Test\n- \'Direct Antiglobulin Test \'\nupdated_at:\n- 2025-09-02 05:00:47.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(35,'1','2025-10-29 15:01:55.000000',7,'Measure','update','---\nshort_name: Direct Coombs\nnlims_code: NLIMS_TT_0047_MWI\nname: Direct Antiglobulin Test\npreferred_name: Direct Coombs Test\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 7\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: Direct Coombs Test\niblis_mapping_name: Direct Coombs Test\n','---\nname:\n- Direct Antiglobulin Test\n- \'Direct Antiglobulin Test \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:55.000000000 +02:00\n'),(36,'1','2025-10-29 15:01:55.000000',241,'Measure','update','---\nshort_name: Ca\nnlims_code: NLIMS_TT_0048_MWI\nname: Calcium\npreferred_name: Ca\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 241\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-10-29 15:01:55.000000000 +02:00\nscientific_name: Calcium\niblis_mapping_name: Ca\n','---\nmoh_code:\n- \'\'\n-\nloinc_code:\n- \'\'\n-\npreferred_name:\n- Ca\n- Calcium\n'),(37,'1','2025-10-29 15:01:55.000000',242,'Measure','update','---\nshort_name: Phos\nnlims_code: NLIMS_TT_0049_MWI\nname: Phosphorus\npreferred_name: Phos\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 242\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-10-29 15:01:55.000000000 +02:00\nscientific_name: Phosphorus\niblis_mapping_name: P\n','---\nmoh_code:\n- \'\'\n-\nloinc_code:\n- \'\'\n-\npreferred_name:\n- Phos\n- Phosphorus\nshort_name:\n- Phos\n- P\n'),(38,'1','2025-10-29 15:01:55.000000',243,'Measure','update','---\nshort_name: Mg\nnlims_code: NLIMS_TT_0050_MWI\nname: \'Magnesium \'\npreferred_name: Mg\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 243\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-10-29 15:01:55.000000000 +02:00\nscientific_name: Magnesium\niblis_mapping_name: Mg\n','---\nname:\n- \'Magnesium \'\n- Magnesium\nmoh_code:\n- \'\'\n-\nloinc_code:\n- \'\'\n-\npreferred_name:\n- Mg\n- Magnesium\n'),(39,'1','2025-10-29 15:01:56.000000',254,'Measure','update','---\nshort_name:\nnlims_code: NLIMS_TI_0254_MWI\nname: Ceftazidime\npreferred_name: Ceftazidime\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 254\nunit: mm\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2021-04-14 14:13:49.000000000 +02:00\nscientific_name:\niblis_mapping_name: Ceftazidime\n','---\nupdated_at:\n- 2021-04-14 14:13:49.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\nshort_name:\n-\n- Ceftazidime\n'),(40,'1','2025-10-29 15:01:56.000000',968,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 968\ntest_type_id:\n-\n- 53\nmeasure_id:\n-\n- 254\ncreated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\n'),(41,'1','2025-10-29 15:01:56.000000',242,'Measure','update','---\nshort_name: P\nnlims_code: NLIMS_TT_0049_MWI\nname: Phosphorus\npreferred_name: Phosphorus\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 242\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-10-29 15:01:55.000000000 +02:00\nscientific_name: Phosphorus\niblis_mapping_name: P\n','---\nupdated_at:\n- 2025-10-29 15:01:55.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\nshort_name:\n- P\n- Phos\n'),(42,'1','2025-10-29 15:01:56.000000',243,'Measure','update','---\nshort_name: Mg\nnlims_code: NLIMS_TT_0050_MWI\nname: Magnesium\npreferred_name: Magnesium\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 243\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-10-29 15:01:55.000000000 +02:00\nscientific_name: Magnesium\niblis_mapping_name: Mg\n','---\nupdated_at:\n- 2025-10-29 15:01:55.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\nshort_name:\n- Mg\n- MGXB\n'),(43,'1','2025-10-29 15:01:56.000000',969,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 969\ntest_type_id:\n-\n- 64\nmeasure_id:\n-\n- 241\ncreated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\n'),(44,'1','2025-10-29 15:01:56.000000',970,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 970\ntest_type_id:\n-\n- 64\nmeasure_id:\n-\n- 242\ncreated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\n'),(45,'1','2025-10-29 15:01:56.000000',971,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 971\ntest_type_id:\n-\n- 64\nmeasure_id:\n-\n- 243\ncreated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:56.000000000 +02:00\n'),(46,'1','2025-10-29 15:01:56.000000',287,'Measure','update','---\nshort_name: fe\nnlims_code: NLIMS_TT_0207_MWI\nname: Iron\npreferred_name: Fe\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 287\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-09-02 05:07:04.000000000 +02:00\nscientific_name: Iron\niblis_mapping_name: Iron\n','---\nupdated_at:\n- 2025-09-02 05:07:04.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\npreferred_name:\n- Fe\n- Iron\n'),(47,'1','2025-10-29 15:01:56.000000',71,'TestType','update','---\nshort_name: VL\nnlims_code: NLIMS_TT_0071_MWI\nname: HIV Viral Load Quantitative HIV Nucleic Acid Test\npreferred_name: Viral Load\nmoh_code:\nloinc_code:\ndescription: \"<p>  To monitor response to ART</p>\"\nid: 71\ntest_category_id: 11\ntargetTAT: \'2 hours \'\nprevalence_threshold: \'\'\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-08-06 14:43:01.000000000 +02:00\nscientific_name: HIV Viral Load Quantitative HIV Nucleic Acid Test\ncan_be_done_on_sex: Both\nassay_format: Nucleic acid test\nhr_cadre_required: Lab technician\niblis_mapping_name: Viral Load\n','---\nname:\n- HIV Viral Load Quantitative HIV Nucleic Acid Test\n- HIV Viral Load\nupdated_at:\n- 2025-08-06 14:43:01.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\n'),(48,'1','2025-10-29 15:01:56.000000',87,'Measure','update','---\nshort_name: PRO\nnlims_code: NLIMS_TT_0079_MWI\nname: Total Protein\npreferred_name: Total Protein\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 87\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:54.000000000 +02:00\nscientific_name: Protein\niblis_mapping_name: Protein\n','---\nupdated_at:\n- 2025-10-29 15:01:54.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\npreferred_name:\n- Total Protein\n- Protein\nshort_name:\n- PRO\n- TP\n'),(49,'1','2025-10-29 15:01:56.000000',570,'Measure','update','---\nshort_name: T4\nnlims_code: NLIMS_TT_0082_MWI\nname: Total Thyroxine\npreferred_name: Total Thyroxine\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 570\nunit: ug/dl\ncreated_at: 2025-04-19 21:55:26.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Total Thyroxine\niblis_mapping_name: TT4\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\npreferred_name:\n- Total Thyroxine\n- TT4\n'),(50,'1','2025-10-29 15:01:56.000000',300,'Measure','update','---\nshort_name: FT3\nnlims_code: NLIMS_TT_0083_MWI\nname: Free Triiodothyronine\npreferred_name: Free T3\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 300\nunit: ng/ml\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Free Triiodothyronine\niblis_mapping_name: FT3\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\npreferred_name:\n- Free T3\n- FT3\n'),(51,'1','2025-10-29 15:01:56.000000',302,'Measure','update','---\nshort_name: T3\nnlims_code: NLIMS_TT_0085_MWI\nname: Total Triiodothyronine\npreferred_name: Total Triiodothyronine(T3)\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 302\nunit: ng/ml\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Total Triiodothyronine\niblis_mapping_name: TT3\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:56.000000000 +02:00\npreferred_name:\n- Total Triiodothyronine(T3)\n- T3\n'),(52,'1','2025-10-29 15:01:57.000000',578,'Measure','update','---\nshort_name: LH\nnlims_code: NLIMS_TT_0093_MWI\nname: Luteinising Hormone\npreferred_name: LH\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 578\nunit: mIU\ncreated_at: 2025-04-19 21:55:26.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Luteinising Hormone \'\niblis_mapping_name: LH\n','---\nname:\n- Luteinising Hormone\n- \'Luteinising Hormone \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(53,'1','2025-10-29 15:01:57.000000',148,'Measure','update','---\nshort_name: HGB\nnlims_code: NLIMS_TT_0105_MWI\nname: \'Haemoglobin \'\npreferred_name: HGB\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 148\nunit: g/dL\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:54.000000000 +02:00\nscientific_name: \'Haemoglobin \'\niblis_mapping_name: HGB\n','---\nname:\n- \'Haemoglobin \'\n- Haemoglobin\nupdated_at:\n- 2025-10-29 15:01:54.000000000 +02:00\n- 2025-10-29 15:01:57.000000000 +02:00\npreferred_name:\n- HGB\n- Haemoglobin\nshort_name:\n- HGB\n- HB\n'),(54,'1','2025-10-29 15:01:57.000000',972,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 972\ntest_type_id:\n-\n- 105\nmeasure_id:\n-\n- 148\ncreated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(55,'1','2025-10-29 15:01:57.000000',108,'TestType','update','---\nshort_name: EID\nnlims_code: NLIMS_TT_0108_MWI\nname: HIV Early Infant Diagnosis Qualitative HIV Nucleic Acid Test\npreferred_name: Early Infant Diagnosis\nmoh_code:\nloinc_code:\ndescription: \"<p>  To diagnose HIV infection in infants &lt; 18 months of age</p><p>\n  \\ </p>\"\nid: 108\ntest_category_id: 11\ntargetTAT: 2 hours\nprevalence_threshold: \'\'\ncreated_at: 2021-04-21 10:50:27.000000000 +02:00\nupdated_at: 2025-09-02 05:00:47.000000000 +02:00\nscientific_name: \'HIV Early Infant Diagnosis Qualitative HIV Nucleic Acid Test \'\ncan_be_done_on_sex: Both\nassay_format: Nucleic Acid Test\nhr_cadre_required: Lab technician\niblis_mapping_name: Early Infant Diagnosis\n','---\nname:\n- HIV Early Infant Diagnosis Qualitative HIV Nucleic Acid Test\n- HIV Early Infant Diagnosis\nupdated_at:\n- 2025-09-02 05:00:47.000000000 +02:00\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(56,'1','2025-10-29 15:01:57.000000',62,'Measure','update','---\nshort_name: RBC\nnlims_code: NLIMS_TI_0604_MWI\nname: Red Blood Cell\npreferred_name: RBC\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 62\nunit: cells/cu.mm\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:51.000000000 +02:00\nscientific_name: Red Blood Cell\niblis_mapping_name: RBC\n','---\nupdated_at:\n- 2025-10-29 15:01:51.000000000 +02:00\n- 2025-10-29 15:01:57.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- RBC\n- Red Blood Cells\nshort_name:\n- RBC\n- RBCs\n'),(57,'1','2025-10-29 15:01:57.000000',61,'Measure','update','---\nshort_name: WBC\nnlims_code: NLIMS_TI_0605_MWI\nname: White Blood Cell\npreferred_name: WBC\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 61\nunit: cells/cu.mm\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:51.000000000 +02:00\nscientific_name: White Blood Cell\niblis_mapping_name: WBC\n','---\nname:\n- White Blood Cell\n- \'White Blood Cell \'\nupdated_at:\n- 2025-10-29 15:01:51.000000000 +02:00\n- 2025-10-29 15:01:57.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- WBC\n- \'White Blood Cells \'\nshort_name:\n- WBC\n- WBCs\n'),(58,'1','2025-10-29 15:01:57.000000',606,'Measure','update','---\nshort_name: \'Bacteria \'\nnlims_code: NLIMS_TI_0606_MWI\nname: Bacteria\npreferred_name: Bacteria\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 5\nid: 606\nunit: \'\'\ncreated_at: 2025-05-06 12:17:12.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Bacteria \'\niblis_mapping_name:\n','---\nname:\n- Bacteria\n- \'Bacteria \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:57.000000000 +02:00\npreferred_name:\n- Bacteria\n- \'Bacteria \'\n'),(59,'1','2025-10-29 15:01:57.000000',973,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 973\ntest_type_id:\n-\n- 111\nmeasure_id:\n-\n- 62\ncreated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(60,'1','2025-10-29 15:01:57.000000',974,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 974\ntest_type_id:\n-\n- 111\nmeasure_id:\n-\n- 61\ncreated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(61,'1','2025-10-29 15:01:57.000000',975,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 975\ntest_type_id:\n-\n- 112\nmeasure_id:\n-\n- 229\ncreated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(62,'1','2025-10-29 15:01:57.000000',976,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 976\ntest_type_id:\n-\n- 113\nmeasure_id:\n-\n- 55\ncreated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:57.000000000 +02:00\n'),(63,'1','2025-10-29 15:01:58.000000',814,'Measure','update','---\nshort_name: \'Trypanosomes \'\nnlims_code: NLIMS_TI_0814_MWI\nname: Trypanosomes\npreferred_name: Trypanosomes\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 5\nid: 814\nunit: \'\'\ncreated_at: 2025-08-13 11:15:47.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Trypanosomes \'\niblis_mapping_name:\n','---\nname:\n- Trypanosomes\n- \'Trypanosomes \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:58.000000000 +02:00\npreferred_name:\n- Trypanosomes\n- \'Trypanosomes \'\n'),(64,'1','2025-10-29 15:01:58.000000',113,'Measure','update','---\nshort_name: VDRL\nnlims_code: NLIMS_TT_0127_MWI\nname: Venereal Disease Research Laboratory\npreferred_name: VDRL\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 113\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:53.000000000 +02:00\nscientific_name: Venereal Disease Research Laboratory\niblis_mapping_name: VDRL\n','---\nupdated_at:\n- 2025-10-29 15:01:53.000000000 +02:00\n- 2025-10-29 15:01:58.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- VDRL\n- Vdrl\nshort_name:\n- VDRL\n- Vdrl\n'),(65,'1','2025-10-29 15:01:58.000000',977,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 977\ntest_type_id:\n-\n- 127\nmeasure_id:\n-\n- 113\ncreated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(66,'1','2025-10-29 15:01:58.000000',139,'TestType','update','---\nshort_name: HVS 1\nnlims_code: NLIMS_TT_0139_MWI\nname: Herpes Simplex Virus Type 1\npreferred_name: Herpes (HSV 1) Test\nmoh_code:\nloinc_code:\ndescription: \"<p>Diagnosis of infections</p>\"\nid: 139\ntest_category_id: 1\ntargetTAT: 1hr\nprevalence_threshold: \'\'\ncreated_at: 2021-07-06 13:59:13.000000000 +02:00\nupdated_at: 2025-09-02 05:00:47.000000000 +02:00\nscientific_name: \" Herpes Simplex Virus Type 1 \"\ncan_be_done_on_sex: Both\nassay_format:\nhr_cadre_required:\niblis_mapping_name: Herpes (HSV 1) Test\n','---\nname:\n- Herpes Simplex Virus Type 1\n- \'Herpes Simplex Virus Type 1 \'\nupdated_at:\n- 2025-09-02 05:00:47.000000000 +02:00\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(67,'1','2025-10-29 15:01:58.000000',978,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 978\ntest_type_id:\n-\n- 139\nmeasure_id:\n-\n- 666\ncreated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(68,'1','2025-10-29 15:01:58.000000',979,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 979\ntest_type_id:\n-\n- 139\nmeasure_id:\n-\n- 667\ncreated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(69,'1','2025-10-29 15:01:58.000000',980,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 980\ntest_type_id:\n-\n- 140\nmeasure_id:\n-\n- 666\ncreated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(70,'1','2025-10-29 15:01:58.000000',981,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 981\ntest_type_id:\n-\n- 140\nmeasure_id:\n-\n- 667\ncreated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(71,'1','2025-10-29 15:01:58.000000',287,'Measure','update','---\nshort_name: fe\nnlims_code: NLIMS_TT_0207_MWI\nname: Iron\npreferred_name: Iron\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 287\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:49.000000000 +02:00\nupdated_at: 2025-10-29 15:01:56.000000000 +02:00\nscientific_name: Iron\niblis_mapping_name: Iron\n','---\ndescription:\n- \'\'\n-\nupdated_at:\n- 2025-10-29 15:01:56.000000000 +02:00\n- 2025-10-29 15:01:58.000000000 +02:00\npreferred_name:\n- Iron\n- Fe\nshort_name:\n- fe\n- Fe\n'),(72,'1','2025-10-29 15:01:58.000000',982,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 982\ntest_type_id:\n-\n- 207\nmeasure_id:\n-\n- 287\ncreated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:58.000000000 +02:00\n'),(73,'1','2025-10-29 15:01:59.000000',95,'Measure','update','---\nshort_name: Microscopy\nnlims_code: NLIMS_TI_0820_MWI\nname: Microscopy\npreferred_name: Microscopy\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 2\nid: 95\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-04-19 23:15:12.000000000 +02:00\nscientific_name: Microscopy\niblis_mapping_name: Microscopy\n','---\nupdated_at:\n- 2025-04-19 23:15:12.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(74,'1','2025-10-29 15:01:59.000000',983,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 983\ntest_type_id:\n-\n- 236\nmeasure_id:\n-\n- 95\ncreated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\n'),(75,'1','2025-10-29 15:01:59.000000',567,'Measure','update','---\nshort_name: RET#\nnlims_code: NLIMS_TI_0635_MWI\nname: Absolute Reticulocyte Count\npreferred_name: RET#\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 567\nunit: 10^6/uL\ncreated_at: 2025-04-19 21:55:24.000000000 +02:00\nupdated_at: 2025-10-29 15:01:54.000000000 +02:00\nscientific_name: Absolute Reticulocyte Count\niblis_mapping_name: RET#\n','---\nupdated_at:\n- 2025-10-29 15:01:54.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- RET#\n- Reticulocyte Count\n'),(76,'1','2025-10-29 15:01:59.000000',984,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 984\ntest_type_id:\n-\n- 248\nmeasure_id:\n-\n- 567\ncreated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\n'),(77,'1','2025-10-29 15:01:59.000000',636,'Measure','update','---\nshort_name: \" Anti-HCV\"\nnlims_code: NLIMS_TT_0249_MWI\nname: Antibodies to Hepatitis C Virus\npreferred_name: Anti-HCV\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 5\nid: 636\nunit: \'\'\ncreated_at: 2025-08-07 14:26:23.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Antibodies to Hepatitis C Virus\niblis_mapping_name:\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\npreferred_name:\n- Anti-HCV\n- \" Anti-HCV\"\n'),(78,'1','2025-10-29 15:01:59.000000',112,'Measure','update','---\nshort_name: RPR\nnlims_code: NLIMS_TT_0251_MWI\nname: Rapid Plasma Reagin\npreferred_name: RPR\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 112\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-08-14 10:52:47.000000000 +02:00\nscientific_name: Rapid Plasma Reagin\niblis_mapping_name: RPR\n','---\nupdated_at:\n- 2025-08-14 10:52:47.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(79,'1','2025-10-29 15:01:59.000000',640,'Measure','update','---\nshort_name: \'Titer \'\nnlims_code: NLIMS_TI_0640_MWI\nname: Titer\npreferred_name: Titer\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 2\nid: 640\nunit: \'\'\ncreated_at: 2025-08-07 14:42:37.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Titer \'\niblis_mapping_name:\n','---\nname:\n- Titer\n- \'Titer \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\npreferred_name:\n- Titer\n- \'Titer \'\n'),(80,'1','2025-10-29 15:01:59.000000',985,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 985\ntest_type_id:\n-\n- 251\nmeasure_id:\n-\n- 112\ncreated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\n'),(81,'1','2025-10-29 15:01:59.000000',121,'Measure','update','---\nshort_name:\nnlims_code: NLIMS_TI_0642_MWI\nname: Measles IgM ELISA-Behring enzygnost\npreferred_name: Measles IgM ELISA-Behring enzygnost\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 121\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2021-04-14 14:13:48.000000000 +02:00\nscientific_name:\niblis_mapping_name: Measles IgM ELISA-Behring enzygnost\n','---\nupdated_at:\n- 2021-04-14 14:13:48.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\nshort_name:\n-\n- Behring Enzygnost\n'),(82,'1','2025-10-29 15:01:59.000000',986,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 986\ntest_type_id:\n-\n- 252\nmeasure_id:\n-\n- 121\ncreated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\n'),(83,'1','2025-10-29 15:01:59.000000',645,'Measure','update','---\nshort_name: CMV\nnlims_code: NLIMS_TI_0645_MWI\nname: Cytomegalovirus\npreferred_name: Cytomegalovirus\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 5\nid: 645\nunit: \'\'\ncreated_at: 2025-08-07 15:40:18.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Cytomegalovirus \'\niblis_mapping_name:\n','---\nname:\n- Cytomegalovirus\n- \'Cytomegalovirus \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\npreferred_name:\n- Cytomegalovirus\n- \'Cytomegalovirus \'\n'),(84,'1','2025-10-29 15:01:59.000000',87,'Measure','update','---\nshort_name: TP\nnlims_code: NLIMS_TT_0079_MWI\nname: Total Protein\npreferred_name: Protein\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 87\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:56.000000000 +02:00\nscientific_name: Protein\niblis_mapping_name: Protein\n','---\nupdated_at:\n- 2025-10-29 15:01:56.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- Protein\n- Total Protein\nshort_name:\n- TP\n- Total Protein\n'),(85,'1','2025-10-29 15:01:59.000000',987,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 987\ntest_type_id:\n-\n- 256\nmeasure_id:\n-\n- 87\ncreated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:01:59.000000000 +02:00\n'),(86,'1','2025-10-29 15:01:59.000000',666,'Measure','update','---\nshort_name: IgM\nnlims_code: NLIMS_TI_0794_MWI\nname: Immunoglobulin M\npreferred_name: IgM\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 666\nunit: mg/dL\ncreated_at: 2025-08-08 09:05:28.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Immunoglobulin M\niblis_mapping_name:\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\npreferred_name:\n- IgM\n- Immunoglobulin M\n'),(87,'1','2025-10-29 15:01:59.000000',667,'Measure','update','---\nshort_name: IgG\nnlims_code: NLIMS_TI_0795_MWI\nname: Immunoglobulin G\npreferred_name: IgG\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 667\nunit: mg/dL\ncreated_at: 2025-08-08 09:05:28.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Immunoglobulin G\niblis_mapping_name:\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\npreferred_name:\n- IgG\n- Immunoglobulin G\n'),(88,'1','2025-10-29 15:01:59.000000',668,'Measure','update','---\nshort_name: Compliment C3\nnlims_code: NLIMS_TI_0668_MWI\nname: Complement Component 3\npreferred_name: Compliment C3\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 668\nunit: mg/dL\ncreated_at: 2025-08-08 09:10:46.000000000 +02:00\nupdated_at: 2025-09-02 05:01:23.000000000 +02:00\nscientific_name: \'Complement Component 3 \'\niblis_mapping_name:\n','---\nname:\n- Complement Component 3\n- \'Complement Component 3 \'\nupdated_at:\n- 2025-09-02 05:01:23.000000000 +02:00\n- 2025-10-29 15:01:59.000000000 +02:00\n'),(89,'1','2025-10-29 15:02:00.000000',572,'Measure','update','---\nshort_name: TSH\nnlims_code: NLIMS_TT_0084_MWI\nname: Thyroid Stimulating Hormone\npreferred_name: TSH\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 572\nunit: uIU/ml\ncreated_at: 2025-04-19 21:55:26.000000000 +02:00\nupdated_at: 2025-08-08 10:46:09.000000000 +02:00\nscientific_name: Thyroid Stimulating Hormone\niblis_mapping_name: TSH\n','---\nupdated_at:\n- 2025-08-08 10:46:09.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(90,'1','2025-10-29 15:02:00.000000',302,'Measure','update','---\nshort_name: T3\nnlims_code: NLIMS_TT_0085_MWI\nname: Total Triiodothyronine\npreferred_name: T3\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 302\nunit: ng/ml\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-10-29 15:01:56.000000000 +02:00\nscientific_name: Total Triiodothyronine\niblis_mapping_name: TT3\n','---\nupdated_at:\n- 2025-10-29 15:01:56.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- T3\n- Total Triiodothyronine(T3)\n'),(91,'1','2025-10-29 15:02:00.000000',300,'Measure','update','---\nshort_name: FT3\nnlims_code: NLIMS_TT_0083_MWI\nname: Free Triiodothyronine\npreferred_name: FT3\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 300\nunit: ng/ml\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-10-29 15:01:56.000000000 +02:00\nscientific_name: Free Triiodothyronine\niblis_mapping_name: FT3\n','---\nupdated_at:\n- 2025-10-29 15:01:56.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- FT3\n- Free T3\n'),(92,'1','2025-10-29 15:02:00.000000',570,'Measure','update','---\nshort_name: T4\nnlims_code: NLIMS_TT_0082_MWI\nname: Total Thyroxine\npreferred_name: TT4\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 570\nunit: ug/dl\ncreated_at: 2025-04-19 21:55:26.000000000 +02:00\nupdated_at: 2025-10-29 15:01:56.000000000 +02:00\nscientific_name: Total Thyroxine\niblis_mapping_name: TT4\n','---\nupdated_at:\n- 2025-10-29 15:01:56.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- TT4\n- Total Thyroxine\n'),(93,'1','2025-10-29 15:02:00.000000',571,'Measure','update','---\nshort_name: FT4\nnlims_code: NLIMS_TT_0081_MWI\nname: Free Thyroxine\npreferred_name: Free Thyroxine\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 571\nunit: ng/dl\ncreated_at: 2025-04-19 21:55:26.000000000 +02:00\nupdated_at: 2025-08-08 10:39:04.000000000 +02:00\nscientific_name: Free Thyroxine\niblis_mapping_name: FT4\n','---\nupdated_at:\n- 2025-08-08 10:39:04.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(94,'1','2025-10-29 15:02:00.000000',680,'Measure','update','---\nshort_name: rT3\nnlims_code: NLIMS_TI_0680_MWI\nname: Reverse T3\npreferred_name: Reverse T3\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 680\nunit: ng/dL\ncreated_at: 2025-08-08 11:15:51.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Reverse T3 \'\niblis_mapping_name:\n','---\nname:\n- Reverse T3\n- \'Reverse T3 \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\npreferred_name:\n- Reverse T3\n- \'Reverse T3 \'\n'),(95,'1','2025-10-29 15:02:00.000000',988,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 988\ntest_type_id:\n-\n- 262\nmeasure_id:\n-\n- 572\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(96,'1','2025-10-29 15:02:00.000000',989,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 989\ntest_type_id:\n-\n- 262\nmeasure_id:\n-\n- 302\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(97,'1','2025-10-29 15:02:00.000000',990,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 990\ntest_type_id:\n-\n- 262\nmeasure_id:\n-\n- 300\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(98,'1','2025-10-29 15:02:00.000000',991,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 991\ntest_type_id:\n-\n- 262\nmeasure_id:\n-\n- 570\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(99,'1','2025-10-29 15:02:00.000000',992,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 992\ntest_type_id:\n-\n- 262\nmeasure_id:\n-\n- 571\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(100,'1','2025-10-29 15:02:00.000000',692,'Measure','update','---\nshort_name: \'Cortisol \'\nnlims_code: NLIMS_TT_0271_MWI\nname: Cortisol (Total)\npreferred_name: Cortisol\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \"<p><br></p>\"\nmeasure_type_id: 1\nid: 692\nunit: \" µg/dL\"\ncreated_at: 2025-08-10 10:06:11.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Cortisol \'\niblis_mapping_name:\n','---\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\npreferred_name:\n- Cortisol\n- \'Cortisol \'\n'),(101,'1','2025-10-29 15:02:00.000000',272,'TestType','update','---\nshort_name: IAT\nnlims_code: NLIMS_TT_0272_MWI\nname: Indirect Antiglobulin Test\npreferred_name: Indirect Coombs Test\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \"<p>To screen for antibodies to red blood cells before a blood transfusion\n  or during pregnancy, To aid in the diagnosis of haemolytic anaemia and blood&nbsp;</p>\"\nid: 272\ntest_category_id: 3\ntargetTAT: 4 hours\nprevalence_threshold:\ncreated_at: 2025-08-10 10:13:58.000000000 +02:00\nupdated_at: 2025-09-02 05:00:47.000000000 +02:00\nscientific_name: \'Indirect Antiglobulin Test \'\ncan_be_done_on_sex: Both\nassay_format:\nhr_cadre_required:\niblis_mapping_name: Indirect Coombs Test\n','---\nname:\n- Indirect Antiglobulin Test\n- \'Indirect Antiglobulin Test \'\nupdated_at:\n- 2025-09-02 05:00:47.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(102,'1','2025-10-29 15:02:00.000000',534,'Measure','update','---\nshort_name: Specimen\nnlims_code: NLIMS_TI_0701_MWI\nname: Specimen / Sample Type\npreferred_name: Specimen\nmoh_code:\nloinc_code:\ndescription:\nmeasure_type_id: 2\nid: 534\nunit:\ncreated_at: 2024-07-18 19:21:13.000000000 +02:00\nupdated_at: 2025-08-10 12:16:26.000000000 +02:00\nscientific_name: Specimen / Sample Type\niblis_mapping_name:\n','---\ndescription:\n-\n- \'\'\nupdated_at:\n- 2025-08-10 12:16:26.000000000 +02:00\n- 2025-10-29 15:02:00.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- Specimen\n- \'Specimen \'\n'),(103,'1','2025-10-29 15:02:00.000000',993,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 993\ntest_type_id:\n-\n- 276\nmeasure_id:\n-\n- 534\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(104,'1','2025-10-29 15:02:00.000000',994,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 994\ntest_type_id:\n-\n- 277\nmeasure_id:\n-\n- 699\ncreated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:00.000000000 +02:00\n'),(105,'1','2025-10-29 15:02:01.000000',995,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 995\ntest_type_id:\n-\n- 279\nmeasure_id:\n-\n- 719\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(106,'1','2025-10-29 15:02:01.000000',996,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 996\ntest_type_id:\n-\n- 284\nmeasure_id:\n-\n- 631\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(107,'1','2025-10-29 15:02:01.000000',749,'Measure','update','---\nshort_name: \"Chromium (Cr)\\t\"\nnlims_code: NLIMS_TI_0749_MWI\nname: Chromium (Cr)\npreferred_name: Chromium (Cr)\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 2\nid: 749\nunit: \'\'\ncreated_at: 2025-08-11 10:10:26.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \"Chromium (Cr)\\t\"\niblis_mapping_name:\n','---\nname:\n- Chromium (Cr)\n- \"Chromium (Cr)\\t\"\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:02:01.000000000 +02:00\npreferred_name:\n- Chromium (Cr)\n- \"Chromium (Cr)\\t\"\n'),(108,'1','2025-10-29 15:02:01.000000',997,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 997\ntest_type_id:\n-\n- 295\nmeasure_id:\n-\n- 683\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(109,'1','2025-10-29 15:02:01.000000',37,'Measure','update','---\nshort_name: pH\nnlims_code: NLIMS_TI_0828_MWI\nname: pH\npreferred_name: pH\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 37\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-05-13 11:08:55.000000000 +02:00\nscientific_name: pH\niblis_mapping_name: pH\n','---\nupdated_at:\n- 2025-05-13 11:08:55.000000000 +02:00\n- 2025-10-29 15:02:01.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(110,'1','2025-10-29 15:02:01.000000',998,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 998\ntest_type_id:\n-\n- 296\nmeasure_id:\n-\n- 37\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(111,'1','2025-10-29 15:02:01.000000',999,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 999\ntest_type_id:\n-\n- 298\nmeasure_id:\n-\n- 655\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(112,'1','2025-10-29 15:02:01.000000',1000,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1000\ntest_type_id:\n-\n- 298\nmeasure_id:\n-\n- 658\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(113,'1','2025-10-29 15:02:01.000000',55,'Measure','update','---\nshort_name: Culture\nnlims_code: NLIMS_TI_0784_MWI\nname: Culture\npreferred_name: Culture\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 55\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-05-13 09:31:56.000000000 +02:00\nscientific_name: Culture\niblis_mapping_name: Culture\n','---\nupdated_at:\n- 2025-05-13 09:31:56.000000000 +02:00\n- 2025-10-29 15:02:01.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(114,'1','2025-10-29 15:02:01.000000',1001,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1001\ntest_type_id:\n-\n- 299\nmeasure_id:\n-\n- 55\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(115,'1','2025-10-29 15:02:01.000000',1002,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1002\ntest_type_id:\n-\n- 300\nmeasure_id:\n-\n- 55\ncreated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:01.000000000 +02:00\n'),(116,'1','2025-10-29 15:02:02.000000',1003,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1003\ntest_type_id:\n-\n- 301\nmeasure_id:\n-\n- 55\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(117,'1','2025-10-29 15:02:02.000000',785,'Measure','update','---\nshort_name: \'Aspergillus IgG \'\nnlims_code: NLIMS_TI_0785_MWI\nname: Aspergillus IgG\npreferred_name: Aspergillus IgG\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 5\nid: 785\nunit: \'\'\ncreated_at: 2025-08-11 11:50:55.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \'Aspergillus IgG \'\niblis_mapping_name:\n','---\nname:\n- Aspergillus IgG\n- \'Aspergillus IgG \'\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\npreferred_name:\n- Aspergillus IgG\n- \'Aspergillus IgG \'\n'),(118,'1','2025-10-29 15:02:02.000000',666,'Measure','update','---\nshort_name: IgM\nnlims_code: NLIMS_TI_0794_MWI\nname: Immunoglobulin M\npreferred_name: Immunoglobulin M\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 666\nunit: mg/dL\ncreated_at: 2025-08-08 09:05:28.000000000 +02:00\nupdated_at: 2025-10-29 15:01:59.000000000 +02:00\nscientific_name: Immunoglobulin M\niblis_mapping_name:\n','---\nupdated_at:\n- 2025-10-29 15:01:59.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\npreferred_name:\n- Immunoglobulin M\n- IgM\n'),(119,'1','2025-10-29 15:02:02.000000',667,'Measure','update','---\nshort_name: IgG\nnlims_code: NLIMS_TI_0795_MWI\nname: Immunoglobulin G\npreferred_name: Immunoglobulin G\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 667\nunit: mg/dL\ncreated_at: 2025-08-08 09:05:28.000000000 +02:00\nupdated_at: 2025-10-29 15:01:59.000000000 +02:00\nscientific_name: Immunoglobulin G\niblis_mapping_name:\n','---\nupdated_at:\n- 2025-10-29 15:01:59.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\npreferred_name:\n- Immunoglobulin G\n- IgG\n'),(120,'1','2025-10-29 15:02:02.000000',1004,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1004\ntest_type_id:\n-\n- 303\nmeasure_id:\n-\n- 666\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(121,'1','2025-10-29 15:02:02.000000',1005,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1005\ntest_type_id:\n-\n- 303\nmeasure_id:\n-\n- 667\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(122,'1','2025-10-29 15:02:02.000000',666,'Measure','update','---\nshort_name: IgM\nnlims_code: NLIMS_TI_0794_MWI\nname: Immunoglobulin M\npreferred_name: IgM\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 666\nunit: mg/dL\ncreated_at: 2025-08-08 09:05:28.000000000 +02:00\nupdated_at: 2025-10-29 15:02:02.000000000 +02:00\nscientific_name: Immunoglobulin M\niblis_mapping_name:\n','---\npreferred_name:\n- IgM\n- Rubella IgM ELISA-Behring enzynost\n'),(123,'1','2025-10-29 15:02:02.000000',1006,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1006\ntest_type_id:\n-\n- 304\nmeasure_id:\n-\n- 666\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(124,'1','2025-10-29 15:02:02.000000',1007,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1007\ntest_type_id:\n-\n- 304\nmeasure_id:\n-\n- 667\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(125,'1','2025-10-29 15:02:02.000000',666,'Measure','update','---\nshort_name: IgM\nnlims_code: NLIMS_TI_0794_MWI\nname: Immunoglobulin M\npreferred_name: Rubella IgM ELISA-Behring enzynost\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 1\nid: 666\nunit: mg/dL\ncreated_at: 2025-08-08 09:05:28.000000000 +02:00\nupdated_at: 2025-10-29 15:02:02.000000000 +02:00\nscientific_name: Immunoglobulin M\niblis_mapping_name:\n','---\npreferred_name:\n- Rubella IgM ELISA-Behring enzynost\n- IgM\n'),(126,'1','2025-10-29 15:02:02.000000',1008,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1008\ntest_type_id:\n-\n- 305\nmeasure_id:\n-\n- 666\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(127,'1','2025-10-29 15:02:02.000000',1009,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1009\ntest_type_id:\n-\n- 305\nmeasure_id:\n-\n- 667\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(128,'1','2025-10-29 15:02:02.000000',1010,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1010\ntest_type_id:\n-\n- 306\nmeasure_id:\n-\n- 797\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(129,'1','2025-10-29 15:02:02.000000',801,'Measure','update','---\nshort_name: ct value\nnlims_code: NLIMS_TI_0807_MWI\nname: Cycle threshold (Ct) value\npreferred_name: Cycle threshold (Ct) value\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 2\nid: 801\nunit: \'\'\ncreated_at: 2025-08-11 16:41:02.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: Cycle Threshold (Ct) Value\niblis_mapping_name:\n','---\nname:\n- Cycle threshold (Ct) value\n- Cycle Threshold (Ct) Value\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\npreferred_name:\n- Cycle threshold (Ct) value\n- Cycle Threshold (Ct) Value\n'),(130,'1','2025-10-29 15:02:02.000000',1011,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1011\ntest_type_id:\n-\n- 308\nmeasure_id:\n-\n- 801\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(131,'1','2025-10-29 15:02:02.000000',805,'Measure','update','---\nshort_name: Influenza B\nnlims_code: NLIMS_TI_0805_MWI\nname: Influenza B RNA\npreferred_name: Influenza B RNA\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 5\nid: 805\nunit: \'\'\ncreated_at: 2025-08-11 16:48:35.000000000 +02:00\nupdated_at: 2025-09-02 05:07:05.000000000 +02:00\nscientific_name: \"Influenza B RNA\\t\"\niblis_mapping_name:\n','---\nname:\n- Influenza B RNA\n- \"Influenza B RNA\\t\"\nupdated_at:\n- 2025-09-02 05:07:05.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\npreferred_name:\n- Influenza B RNA\n- \"Influenza B RNA\\t\"\n'),(132,'1','2025-10-29 15:02:02.000000',801,'Measure','update','---\nshort_name: ct value\nnlims_code: NLIMS_TI_0807_MWI\nname: Cycle Threshold (Ct) Value\npreferred_name: Cycle Threshold (Ct) Value\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 2\nid: 801\nunit: \'\'\ncreated_at: 2025-08-11 16:41:02.000000000 +02:00\nupdated_at: 2025-10-29 15:02:02.000000000 +02:00\nscientific_name: Cycle Threshold (Ct) Value\niblis_mapping_name:\n','---\nname:\n- Cycle Threshold (Ct) Value\n- Cycle threshold (Ct) value\npreferred_name:\n- Cycle Threshold (Ct) Value\n- Cycle threshold (Ct) value\n'),(133,'1','2025-10-29 15:02:02.000000',1012,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1012\ntest_type_id:\n-\n- 310\nmeasure_id:\n-\n- 801\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(134,'1','2025-10-29 15:02:02.000000',311,'TestType','update','---\nshort_name: Ebola\nnlims_code: NLIMS_TT_0311_MWI\nname: Ebola Nucleic Acid Test\npreferred_name: Ebola\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \"<p>Detect Ebola virus RNA in blood, oral swab, or other body fluids</p>\"\nid: 311\ntest_category_id: 13\ntargetTAT: 24 hours\nprevalence_threshold:\ncreated_at: 2025-08-13 10:53:17.000000000 +02:00\nupdated_at: 2025-09-02 05:00:47.000000000 +02:00\nscientific_name: \" Ebola Nucleic Acid Test\"\ncan_be_done_on_sex: Both\nassay_format:\nhr_cadre_required:\niblis_mapping_name: Ebola\n','---\nname:\n- Ebola Nucleic Acid Test\n- \" Ebola Nucleic Acid Test\"\nupdated_at:\n- 2025-09-02 05:00:47.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(135,'1','2025-10-29 15:02:02.000000',1013,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1013\ntest_type_id:\n-\n- 316\nmeasure_id:\n-\n- 95\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(136,'1','2025-10-29 15:02:02.000000',796,'Measure','update','---\nshort_name: Microscopy findings\nnlims_code: NLIMS_TI_0821_MWI\nname: Microscopy findings\npreferred_name: Microscopy findings\nmoh_code: \'\'\nloinc_code: \'\'\ndescription: \'\'\nmeasure_type_id: 4\nid: 796\nunit: \'\'\ncreated_at: 2025-08-11 15:40:14.000000000 +02:00\nupdated_at: 2025-10-29 15:01:51.000000000 +02:00\nscientific_name: Microscopy findings\niblis_mapping_name:\n','---\nname:\n- Microscopy findings\n- Microscopy Findings\nupdated_at:\n- 2025-10-29 15:01:51.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\npreferred_name:\n- Microscopy findings\n- Microscopy Findings\nshort_name:\n- Microscopy findings\n- Microscopy\n'),(137,'1','2025-10-29 15:02:02.000000',1014,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1014\ntest_type_id:\n-\n- 317\nmeasure_id:\n-\n- 796\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(138,'1','2025-10-29 15:02:02.000000',1015,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1015\ntest_type_id:\n-\n- 317\nmeasure_id:\n-\n- 797\ncreated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:02.000000000 +02:00\n'),(139,'1','2025-10-29 15:02:02.000000',91,'Measure','update','---\nshort_name: Specific gravity\nnlims_code: NLIMS_TI_0827_MWI\nname: Specific gravity\npreferred_name: Specific gravity\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 91\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-10-29 15:01:52.000000000 +02:00\nscientific_name: Specific gravity\niblis_mapping_name: Specific gravity\n','---\nname:\n- Specific gravity\n- Specific Gravity\nupdated_at:\n- 2025-10-29 15:01:52.000000000 +02:00\n- 2025-10-29 15:02:02.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- Specific gravity\n- Specific Gravity\nshort_name:\n- Specific gravity\n- Specific Gravity\n'),(140,'1','2025-10-29 15:02:03.000000',90,'Measure','update','---\nshort_name: Glu\nnlims_code: NLIMS_TT_0038_MWI\nname: Glucose\npreferred_name: Glucose\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 90\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-08-06 14:01:15.000000000 +02:00\nscientific_name: Glucose\niblis_mapping_name: Glucose\n','---\nupdated_at:\n- 2025-08-06 14:01:15.000000000 +02:00\n- 2025-10-29 15:02:03.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\nshort_name:\n- Glu\n- Glucose\n'),(141,'1','2025-10-29 15:02:03.000000',40,'Measure','update','---\nshort_name: Ketones\nnlims_code: NLIMS_TI_0831_MWI\nname: Ketones\npreferred_name: Ketones\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 40\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-05-13 11:08:55.000000000 +02:00\nscientific_name: Ketones\niblis_mapping_name: Ketones\n','---\nname:\n- Ketones\n- Ketone\nupdated_at:\n- 2025-05-13 11:08:55.000000000 +02:00\n- 2025-10-29 15:02:03.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\npreferred_name:\n- Ketones\n- Ketone\nshort_name:\n- Ketones\n- Ketone\n'),(142,'1','2025-10-29 15:02:03.000000',43,'Measure','update','---\nshort_name: Bilirubin\nnlims_code: NLIMS_TI_0832_MWI\nname: Bilirubin\npreferred_name: Bilirubin\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 43\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-05-13 11:08:55.000000000 +02:00\nscientific_name: Bilirubin\niblis_mapping_name: Bilirubin\n','---\nupdated_at:\n- 2025-05-13 11:08:55.000000000 +02:00\n- 2025-10-29 15:02:03.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(143,'1','2025-10-29 15:02:03.000000',84,'Measure','update','---\nshort_name: Urobilinogen\nnlims_code: NLIMS_TI_0833_MWI\nname: Urobilinogen\npreferred_name: Urobilinogen\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 1\nid: 84\nunit: mg/dl\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-05-13 11:08:55.000000000 +02:00\nscientific_name: Urobilinogen\niblis_mapping_name: Urobilinogen\n','---\nupdated_at:\n- 2025-05-13 11:08:55.000000000 +02:00\n- 2025-10-29 15:02:03.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(144,'1','2025-10-29 15:02:03.000000',1016,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1016\ntest_type_id:\n-\n- 318\nmeasure_id:\n-\n- 91\ncreated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\n'),(145,'1','2025-10-29 15:02:03.000000',1017,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1017\ntest_type_id:\n-\n- 318\nmeasure_id:\n-\n- 37\ncreated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\n'),(146,'1','2025-10-29 15:02:03.000000',1018,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1018\ntest_type_id:\n-\n- 318\nmeasure_id:\n-\n- 90\ncreated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\n'),(147,'1','2025-10-29 15:02:03.000000',1019,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1019\ntest_type_id:\n-\n- 318\nmeasure_id:\n-\n- 40\ncreated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\n'),(148,'1','2025-10-29 15:02:03.000000',1020,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1020\ntest_type_id:\n-\n- 318\nmeasure_id:\n-\n- 43\ncreated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\n'),(149,'1','2025-10-29 15:02:03.000000',1021,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1021\ntest_type_id:\n-\n- 318\nmeasure_id:\n-\n- 84\ncreated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\nupdated_at:\n-\n- 2025-10-29 15:02:03.000000000 +02:00\n'),(150,'1','2025-10-30 10:52:05.000000',96,'Measure','update','---\nshort_name: Blood film\nnlims_code: NLIMS_TI_0096_MWI\nname: Blood film\npreferred_name: Blood film\nmoh_code:\nloinc_code:\ndescription: \'\'\nmeasure_type_id: 5\nid: 96\nunit: \'\'\ncreated_at: 2021-04-14 14:13:48.000000000 +02:00\nupdated_at: 2025-04-29 09:52:06.000000000 +02:00\nscientific_name: Blood film\niblis_mapping_name: Blood film\n','---\nupdated_at:\n- 2025-04-29 09:52:06.000000000 +02:00\n- 2025-10-30 10:52:05.000000000 +02:00\nmoh_code:\n-\n- \'\'\nloinc_code:\n-\n- \'\'\n'),(151,'1','2025-10-30 10:52:05.000000',1022,'TesttypeMeasure','create',NULL,'---\nid:\n-\n- 1022\ntest_type_id:\n-\n- 322\nmeasure_id:\n-\n- 96\ncreated_at:\n-\n- 2025-10-30 10:52:05.000000000 +02:00\nupdated_at:\n-\n- 2025-10-30 10:52:05.000000000 +02:00\n'),(152,'1','2025-10-30 11:17:22.000000',29,'TestType','update','---\nshort_name: ABO\nnlims_code: NLIMS_TT_0029_MWI\nname: ABO And RhD Blood Grouping\npreferred_name: ABO Blood Grouping\nmoh_code:\nloinc_code:\ndescription: \"<p>To determine the ABO and RhD blood typing for all antenatal women</p>\"\nid: 29\ntest_category_id: 5\ntargetTAT: 30 Minutes\nprevalence_threshold: \'\'\ncreated_at: 2021-04-14 14:13:47.000000000 +02:00\nupdated_at: 2025-05-06 11:32:39.000000000 +02:00\nscientific_name: ABO And RhD Blood Grouping\ncan_be_done_on_sex: Both\nassay_format: tile method\nhr_cadre_required: clinicians, nurses and any other trained staff\niblis_mapping_name: ABO Blood Grouping\n','---\ntest_category_id:\n- 5\n- 3\nupdated_at:\n- 2025-05-06 11:32:39.000000000 +02:00\n- 2025-10-30 11:17:22.000000000 +02:00\n'),(153,'1','2025-10-30 11:22:44.000000',108,'TestType','update','---\nshort_name: EID\nnlims_code: NLIMS_TT_0108_MWI\nname: HIV Early Infant Diagnosis\npreferred_name: Early Infant Diagnosis\nmoh_code:\nloinc_code:\ndescription: \"<p>  To diagnose HIV infection in infants &lt; 18 months of age</p><p>\n  \\ </p>\"\nid: 108\ntest_category_id: 11\ntargetTAT: 2 hours\nprevalence_threshold: \'\'\ncreated_at: 2021-04-21 10:50:27.000000000 +02:00\nupdated_at: 2025-10-29 15:01:57.000000000 +02:00\nscientific_name: \'HIV Early Infant Diagnosis Qualitative HIV Nucleic Acid Test \'\ncan_be_done_on_sex: Both\nassay_format: Nucleic Acid Test\nhr_cadre_required: Lab technician\niblis_mapping_name: Early Infant Diagnosis\n','---\nupdated_at:\n- 2025-10-29 15:01:57.000000000 +02:00\n- 2025-10-30 11:22:44.000000000 +02:00\npreferred_name:\n- Early Infant Diagnosis\n- HIV Early Infant Diagnosis\n'),(154,'1','2025-11-28 08:36:42.000000',846,'TesttypeSpecimentype','create',NULL,'---\nid:\n-\n- 846\ntest_type_id:\n-\n- 10\nspecimen_type_id:\n-\n- 11\ncreated_at:\n-\n- 2025-11-28 08:36:42.000000000 +02:00\nupdated_at:\n-\n- 2025-11-28 08:36:42.000000000 +02:00\n');
/*!40000 ALTER TABLE `versions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-11-28  8:39:18
