def specimen_types
  [{ 'id' => 1,
     'name' => 'Sputum',
     'description' => 'Thick mucus or phlegm that is coughed up from the lungs and lower respiratory tract (bronchi and bronchioles)',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Sputum',
     'scientific_name' => 'Sputum' },
   { 'id' => 2,
     'name' => 'Cerebrospinal Fluid',
     'description' => 'Clear, colorless body fluid found in the brain and spinal cord.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'CSF',
     'scientific_name' => 'Cerebrospinal Fluid' },
   { 'id' => 3,
     'name' => 'Whole Blood',
     'description' => 'Unprocessed blood that has been collected directly from a patient or donor without any separation of its components.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Whole Blood',
     'scientific_name' => 'Sanguis totus' },
   { 'id' => 4,
     'name' => 'Pleural Fluid',
     'description' => 'Serous fluid found in the pleural cavity, the space between the visceral pleura (lining the lungs) and the parietal pleura (lining the chest wall)',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Pleural Fluid',
     'scientific_name' => 'Pleural Fluid' },
   { 'id' => 5,
     'name' => 'Ascitic Fluid',
     'description' => 'Fluid that accumulates in the peritoneal cavity (the space between the abdominal organs and the abdominal wall) in cases of ascites',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Ascitic Fluid',
     'scientific_name' => 'Ascitic Fluid' },
   { 'id' => 6,
     'name' => 'Pericardial Fluid',
     'description' => 'Serous fluid found in the pericardial cavity, which surrounds the heart.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Pericardial Fluid',
     'scientific_name' => 'Pericardial Fluid' },
   { 'id' => 7,
     'name' => 'Peritoneal Fluid',
     'description' => 'Serous fluid that fills the peritoneal cavity, the space between the parietal peritoneum (the lining of the abdominal wall) and the visceral peritoneum (the lining of abdominal organs)',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Peritoneal Fluid',
     'scientific_name' => 'Peritoneal Fluid' },
   { 'id' => 8,
     'name' => 'HVS',
     'description' => '',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 9,
     'name' => 'Swabs',
     'description' => '',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 10,
     'name' => 'Pus',
     'description' => 'Thick, yellowish or greenish fluid that is produced during the inflammatory response to infection, particularly when the body is fighting off bacterial infections.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Pus',
     'scientific_name' => 'Purulent Exudate' },
   { 'id' => 11,
     'name' => 'Stool',
     'description' => 'Solid or semi-solid waste product of digestion that is expelled from the body through the rectum during defecation',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Stool ',
     'scientific_name' => 'Feces' },
   { 'id' => 12,
     'name' => 'Urine',
     'description' => 'Liquid waste product produced by the kidneys as they filter blood to remove excess water, waste products, and toxins',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Urine',
     'scientific_name' => 'Urina' },
   { 'id' => 13,
     'name' => 'Other',
     'description' => '',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 14,
     'name' => 'Semen',
     'description' => 'Milky-white fluid that is ejaculated from the male reproductive tract during sexual climax.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Semen',
     'scientific_name' => 'Seminal Fluid' },
   { 'id' => 15,
     'name' => 'Swab',
     'description' => '',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 16,
     'name' => 'Synovial Fluid',
     'description' => '',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 17,
     'name' => 'Plasma',
     'description' => 'Liquid component of blood, making up about 55% of total blood volume. It is a yellowish fluid that serves as the medium for transporting cells, nutrients, waste products, hormones, and proteins throughout the body',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Plasma',
     'scientific_name' => 'Plasma ' },
   { 'id' => 18,
     'name' => 'Dried Blood Spot(Free drop to DBS card)',
     'description' => 'A finger or heel is pricked using a lancet. Blood directly drops onto a DBS filter paper card by gravity',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'DBS(Free drop to DBS card)',
     'scientific_name' => 'Dried Blood Spot(Free drop to DBS card)' },
   { 'id' => 19,
     'name' => 'Dried Blood Spot(Using capillary tube)',
     'description' => 'Blood is collected into a capillary tube (e.g., heparinized or plain). A precise volume (e.g., 50 µL) is then applied to the DBS card.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'DBS(Using Capillary Tube)',
     'scientific_name' => 'Dried Blood Spot(Using Capillary Tube)' },
   { 'id' => 20,
     'name' => 'Serum',
     'description' => 'Clear, yellowish fluid that remains after blood has clotted and the clotting factors (like fibrinogen) have been removed',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Serum',
     'scientific_name' => 'Serum sanguinis' },
   { 'id' => 21,
     'name' => 'Tissue Biopsy',
     'description' => 'Medical procedure in which a small sample of tissue is taken from the body for diagnostic examination.',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Tissue Biopsy',
     'scientific_name' => 'Biopsia tisular' },
   { 'id' => 22,
     'name' => 'Gastric aspirate',
     'description' => nil,
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 23,
     'name' => 'Nasopharyngeal Swab',
     'description' => 'Sample taken from the nasopharynx (the upper part of the throat, behind the nose)',
     'created_at' => '2021-04-14T14:13:47.000+02:00',
     'updated_at' => '2021-04-14T14:13:47.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => 'Nasopharyngeal Swab',
     'scientific_name' => 'Nasopharyngeal Swab' },
   { 'id' => 24,
     'name' => 'FNA',
     'description' => '',
     'created_at' => '2021-04-21T09:58:52.000+02:00',
     'updated_at' => '2021-04-21T09:58:52.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 25,
     'name' => 'Fluid Aspirate',
     'description' => '',
     'created_at' => '2021-05-01T14:32:07.000+02:00',
     'updated_at' => '2021-05-01T14:32:07.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 26,
     'name' => 'Aspirate',
     'description' => '',
     'created_at' => '2021-05-04T05:31:31.000+02:00',
     'updated_at' => '2021-05-04T05:31:31.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 27,
     'name' => 'Tissue',
     'description' => '',
     'created_at' => '2021-05-04T07:26:50.000+02:00',
     'updated_at' => '2021-05-04T07:26:50.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 28,
     'name' => 'not_specified',
     'description' => '',
     'created_at' => '2021-06-14T11:12:59.000+02:00',
     'updated_at' => '2021-06-14T11:12:59.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 29,
     'name' => 'Nasolpharyngeal',
     'description' => '',
     'created_at' => '2021-06-18T11:55:16.000+02:00',
     'updated_at' => '2021-06-18T11:55:16.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 30,
     'name' => 'Oralpharyngeal',
     'description' => '',
     'created_at' => '2021-06-18T12:23:06.000+02:00',
     'updated_at' => '2021-06-18T12:23:06.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
   { 'id' => 31,
     'name' => 'DBS 70ml',
     'description' => '',
     'created_at' => '2021-09-28T11:47:05.000+02:00',
     'updated_at' => '2021-09-28T11:47:05.000+02:00',
     'moh_code' => nil,
     'nlims_code' => nil,
     'loinc_code' => nil,
     'preferred_name' => nil,
     'scientific_name' => nil },
     {
       'name' => 'Capillary whole blood',
       'description' => 'Blood obtained from capillaries, which are the tiny blood vessels connecting arterioles and venules',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Capillary whole blood',
       'scientific_name' => 'Capillary whole blood'
     },
     {
       'name' => 'Cervical cell',
       'description' => 'Cells obtained from the cervix, which is the lower part of the uterus that connects to the vagina',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Cervical Cell',
       'scientific_name' => 'Cervical Cytology/Cervical Epithelial Cells'
     },
     {
       'name' => 'Early Morning Urine',
       'description' => 'Urine collected immediately after waking up. It is more concentrated due to overnight accumulation, making it useful for detecting various biomarkers and infections',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Early Morning Urine',
       'scientific_name' => 'Urina matutina'
     },
     {
       'name' => 'Oral Fluid',
       'description' => 'A clear, watery, and enzyme-rich fluid produced by the salivary glands in the mouth',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => ' Oral Fluid',
       'scientific_name' => 'Saliva'
     }, {
       'name' => 'Rectal Swab',
       'description' => 'A sample collected by inserting a sterile swab into the rectum to obtain mucus or stool',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => ' Rectal Swab',
       'scientific_name' => 'Rectal Swab'
     }, {
       'name' => 'EDTA-Venous Blood',
       'description' => 'Venous blood that has been collected in a tube containing EDTA (Ethylenediaminetetraacetic acid) as an anticoagulant',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => ' EDTA-Venous Blood',
       'scientific_name' => 'EDTA-Venous Blood'
     },
     {
       'name' => 'Sterile Venous Whole Blood',
       'description' => 'Venous blood collected under sterile conditions, maintaining its original composition without separation of plasma, serum, or cellular components.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Sterile Venous Whole Blood',
       'scientific_name' => 'Sterile Venous Whole Blood'
     },
     {
       'name' => 'Sterile Body Fluids',
       'description' => 'Fluids naturally present in body compartments that are normally free from microorganisms (bacteria, viruses, fungi, and parasites).',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Sterile Body Fluids',
       'scientific_name' => 'Sterile Body Fluids'
     },
     {
       'name' => 'Skin Scrape',
       'description' => 'Diagnostic procedure used to collect a sample of skin cells or skin lesions for microscopic examination.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Skin Scrape',
       'scientific_name' => 'Dermal Scraping'
     },
     {
       'name' => 'Skin Snip Biopsy',
       'description' => 'Diagnostic procedure primarily used for onchocerciasis (also known as river blindness), which is caused by the parasitic worm Onchocerca volvulus.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Skin Snip Biopsy',
       'scientific_name' => 'Onchocerca skin snip biopsy'
     },
     {
       'name' => 'Extrapulmonary Specimens',
       'description' => 'Medical procedure used to collect a sample of fluid from the lower respiratory tract (lungs) for diagnostic purposes.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Bronchoalveolar Lavage (BAL)',
       'scientific_name' => 'Bronchoalveolar lavage'
     },
     {
       'name' => 'Extrapulmonary Specimens',
       'description' => 'Samples collected from areas outside the lungs, which are often used to diagnose systemic infections, diseases affecting organs and tissues outside the respiratory system, or metastatic diseases.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Extrapulmonary Specimens',
       'scientific_name' => 'Extrapulmonary Specimens'
     },
     {
       'name' => 'Citrate Plasma',
       'description' => 'Plasma collected from blood that has been anticoagulated using sodium citrate.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Citrate Plasma',
       'scientific_name' => 'Plasma citrata'
     },
     {
       'name' => 'Sodium Citrate Whole Blood',
       'description' => 'Whole blood that has been collected with sodium citrate as the anticoagulant.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Sodium Citrate Whole Blood',
       'scientific_name' => 'Sodium Citrate Whole Blood'
     },
     {
       'name' => 'Venous Whole Blood',
       'description' => 'Deoxygenated blood which travels from the peripheral blood vessels, through the venous system into the right atrium of the heart.',
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Venous Whole Blood',
       'scientific_name' => 'Deoxygenated blood'
     },
     {
       'name' => 'Nasal Swab',
       'description' => "Sterile swab collected from the inside of a patient's nose",
       'moh_code' => nil,
       'nlims_code' => nil,
       'loinc_code' => nil,
       'preferred_name' => 'Nasal Swab',
       'scientific_name' => 'Swab nasalis'
     }]
end
